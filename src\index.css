@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Quicksand:wght@300..700&family=Poppins:ital,wght@0,100..900;1,100..900&display=swap");
@import "tailwindcss";

/* Tailwind Theme Layer */
@theme {
  /* Navy Palette (from your provided values) */
  --color-navy-darkest: hsl(225 73% 10%);
  --color-navy-darker:  hsl(222 75% 16%);
  --color-navy-dark:    hsl(216 84% 19%);
  --color-navy-medium:  hsl(221 86% 24%);
  --color-navy-light:   hsl(224 63% 35%);

  /* Text colors */
  --color-text: hsl(220 15% 90%);
  --color-text-muted: hsl(220 10% 65%);
  --color-text-inverse: hsl(222 15% 8%);

  /* Accent colors */
  --color-primary: var(--color-navy-medium);
  --color-primary-contrast: hsl(0 0% 100%);
  --color-accent-1: hsl(210 80% 62%);
  --color-accent-2: hsl(195 78% 52%);
  --color-success: hsl(161 70% 40%);
  --color-warning: hsl(45 100% 62%);
  --color-danger: hsl(2 82% 56%);
  --color-link-hover: hsl(224 72% 58%);

  /* Background images */
  --background-bg-gradient: linear-gradient(
    180deg,
    hsla(221, 86%, 24%, 0.75),
    hsla(224, 63%, 35%, 0.65)
  );
  --background-btn-gradient: linear-gradient(
    180deg,
    hsl(221 86% 30%) 0%,
    hsl(224 63% 35%) 100%
  );
  --background-glass-shine: linear-gradient(
    to bottom right,
    hsl(0 0% 100% / 0.1),
    hsl(0 0% 100% / 0) 40%
  );

  /* Glassmorphism Variables */
  --glass-bg: hsl(220 25% 10% / 0.55);
  --glass-soft-bg: hsl(220 25% 12% / 0.35);
  --glass-strong-bg: hsl(220 25% 8% / 0.75);
  --glass-border: hsl(220 25% 90% / 0.08);
  --glass-shadow: 0 8px 24px hsl(220 80% 2% / 0.65),
    0 2px 6px hsl(220 80% 2% / 0.35);
  --glass-blur: 16px;
  --glass-saturation: 140%;

  /* Radius & Ring */
  --radius: 14px;
  --ring: 0 0 0 1px var(--glass-border);
}

/* Utilities for special effects */
@layer utilities {
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation));
    -webkit-backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation));
    border: 1px solid var(--glass-border);
    border-radius: var(--radius);
    box-shadow: var(--glass-shadow);
    outline: var(--ring);
  }
  .glass-soft {
    background: var(--glass-soft-bg);
  }
  .glass-strong {
    background: var(--glass-strong-bg);
  }
  .glass-shine {
    background-image: var(--background-glass-shine);
  }
  .surface {
    background: linear-gradient(
      180deg,
      hsl(220 25% 14% / 0.65),
      hsl(220 25% 10% / 0.65)
    );
    border: 1px solid hsl(220 25% 90% / 0.06);
    border-radius: 12px;
    box-shadow: 0 6px 20px hsl(220 80% 2% / 0.55);
  }
}

/* Base for theme application */
@layer base {
  html.navy-theme,
  .navy-theme body,
  .navy-theme .page {
    background: var(--background-bg-gradient), var(--color-navy-darkest);
    color: var(--color-text);
    font-family: "Poppins", sans-serif;
  }
  .navy-theme {
    color-scheme: dark;
  }
  .navy-theme a {
    color: var(--color-navy-light);
    text-decoration: none;
  }
  .navy-theme a:hover {
    color: var(--color-link-hover);
  }
}

.hallow{
  -webkit-text-stroke: 1px var(--color-text);
  color: transparent;
  font-family: Arial, Helvetica, sans-serif;
  font-weight: 600;
  
  
}


