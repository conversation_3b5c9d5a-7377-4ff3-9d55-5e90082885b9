import React from 'react'
import { useGSAP } from '@gsap/react'
import { gsap } from 'gsap'

const NavBar = () => {
  useGSAP(() => {
    gsap.from('ul li', { duration: 1, y: -100, opacity: 0, ease: 'power3.out', stagger: 0.5 });
  })
  return (
    <>
      <div className=" w-full px-8 py-4 mx-auto mt-5 flex justify-between items-center">
        <h1 className="text-3xl font-bold lg:ml-12 ">Portfolio</h1>

        <ul className="justify-center items-center gap-10 px-8 py-4 w-fit hidden md:flex lg:mr-12">
          <li className="relative group">
            <a href="#home" className="block py-2 text-2xl transition-colors duration-300 group-hover:text-[var(--color-accent-1)]">
              Home
            </a>
            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[var(--color-accent-1)] transition-all duration-400 ease-out group-hover:w-full"></span>
          </li>

          <li className="relative group">
            <a href="#about" className="block py-2 text-2xl transition-colors duration-300 group-hover:text-[var(--color-accent-1)]">
              About
            </a>
            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[var(--color-accent-1)] transition-all duration-400 ease-out group-hover:w-full"></span>
          </li>

          <li className="relative group">
            <a href="#skills" className="block py-2 text-2xl transition-colors duration-300 group-hover:text-[var(--color-accent-1)]">
              Skills
            </a>
            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[var(--color-accent-1)] transition-all duration-400 ease-out group-hover:w-full"></span>
          </li>

          <li className="relative group">
            <a href="#projects" className="block py-2 text-2xl transition-colors duration-300 group-hover:text-[var(--color-accent-1)]">
              Projects
            </a>
            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[var(--color-accent-1)] transition-all duration-400 ease-out group-hover:w-full"></span>
          </li>

          <li className="relative group">
            <a href="#contact" className="block py-2 text-2xl transition-colors duration-300 group-hover:text-[var(--color-accent-1)]">
              Contact
            </a>
            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[var(--color-accent-1)] transition-all duration-400 ease-out group-hover:w-full"></span>
          </li>
        </ul>
      </div>
    </>
  )
}

export default NavBar